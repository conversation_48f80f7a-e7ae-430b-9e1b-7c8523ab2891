// hooks/usePackageDetail.ts
import { useState, useEffect } from 'react'

interface PackageDetail {
  id: string
  name: string
  slug: string
  price: string
  discountPrice: string
  duration: string
  altitude: string
  meals: string
  groupSize: string
  grade: string
  bestSeason: string
  transport: string
  activityPerDay: string
  overviewDescription: string
  thumbnail: string
  mainImage: string
  accomodation: string
  type: string
  activity: {
    name: string
  }
  region: {
    name: string
  }
  highlights?: {
    title: string
    description: string
  }
  description?: {
    title: string
    description: string
  }
  itinerary?: Array<{
    day: number
    title: string
    highestAltitude: string
    trekDistance: string
    trekDuration: string
    drivingHours: string
    flightHours: string
    heading: string
    activity: string
    image: string
  }>
  faq?: Array<{
    question: string
    answer: string
    published: boolean
  }>
  review?: Array<{
    name: string
    rating: number
    comment: string
    reviewImage: string
  }>
  gallery?: {
    PackageGalleryImage: Array<{
      image: string
      alt?: string
    }>
  }
}

interface ApiResponse {
  statusCode: number
  success: boolean
  data: PackageDetail
  message: string
}

export const usePackageDetail = (slug: string) => {
  const [packageDetail, setPackageDetail] = useState<PackageDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!slug) return

    const fetchPackageDetail = async () => {
      try {
        setLoading(true)
        setError(null)
        
        const response = await fetch(`https://api.trailandtreknepal.com/package/slug/${slug}`)
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        
        const data: ApiResponse = await response.json()
        
        if (!data.success) {
          throw new Error(data.message || 'Failed to fetch package details')
        }
        
        setPackageDetail(data.data)
        
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An error occurred'
        setError(errorMessage)
        console.error('Error fetching package detail:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchPackageDetail()
  }, [slug])

  return { packageDetail, loading, error }
}