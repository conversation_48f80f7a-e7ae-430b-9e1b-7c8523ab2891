'use client'

import PricingCard from '@/components/common/sidebar/pricingcard'
import TrekInfo from '@/components/common/trek-overview/trek'
import Gears from '@/modules/fastpacking/components/gears'
import Highlight, { HighlightItem } from '@/modules/fastpacking/components/highlight'
import Mapandchart from '@/modules/fastpacking/components/map'
import Overview from '@/modules/fastpacking/components/overview'
import GalleryWithMore from '@/modules/fastpacking/components/video-photo-section'
import PackageInformation from '@/modules/fastpacking/components/detail'
import FAQSection from '@/modules/fastpacking/components/faq-section'
import HeroSection from '@/modules/fastpacking/components/hero'
import TrekInclusionsExclusions, { Category } from '@/modules/fastpacking/components/included-excluded'
import ItineraryDetailed from '@/modules/fastpacking/components/itinerary-detailed'
import ItinerarySummary, { DaySummary } from '@/modules/fastpacking/components/itinerary-summary'
import StackedReviews, { Review } from '@/modules/fastpacking/components/review'
import TravelersReview, { ReviewVideo } from '@/modules/fastpacking/components/review-video'
import { useRouter, useParams } from 'next/navigation'
import React from 'react'
import { AlertTriangle, Backpack, Ban, FileText, HandCoins, Hotel, ShieldCheck, UserCheck, Utensils, UtensilsCrossed } from 'lucide-react'
import { ScrollSpyTabs } from '@/components/common/sticky-scroll/sticky-scroll'
import { usePackageDetail } from '@/modules/package/queries/get-package-detail'

const TrekDetailPage = () => {
    const params = useParams()
    const router = useRouter()
    const slug = params.slug as string
    
    const { packageDetail, loading, error } = usePackageDetail(slug)

    const handleInquireNow = () => {
        router.push('/contact')
    }

    const handleDatesPrice = () => {
        router.push('/dates-price')
    }

    const handleBookNow = () => {
        router.push('/booking')
    }

    const sectionIds = [
        { id: "trek-info", label: "Trek Info" },
        { id: "highlights", label: "Highlights" },
        { id: "overview", label: "Overview" },
        { id: "gallery", label: "Gallery" },
        { id: "itinerary", label: "Itinerary" },
        { id: "map", label: "Map" },
        { id: "inclusions", label: "Inclusions/Exclusions" },
        { id: "gears", label: "Gears" },
        { id: "package-info", label: "Package Information" },
        { id: "reviews", label: "Reviews" },
    ]

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <p className="text-lg">Loading trek details...</p>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <div className="text-red-600 mb-4">
                        <p className="text-lg font-semibold">Error loading trek details</p>
                        <p className="text-sm text-gray-600 mt-2">{error}</p>
                    </div>
                    <button 
                        onClick={() => window.location.reload()}
                        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Try Again
                    </button>
                </div>
            </div>
        )
    }

    if (!packageDetail) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <p className="text-lg">Trek not found</p>
                    <button 
                        onClick={() => router.push('/trekking')}
                        className="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                    >
                        Back to Treks
                    </button>
                </div>
            </div>
        )
    }

    // Map API data to component props
    const customTrekData = {
        destination: `${packageDetail.region?.name} Region`,
        accommodation: packageDetail.accomodation,
        duration: packageDetail.duration,
        maxElevation: `${packageDetail.altitude}m`,
        group: packageDetail.groupSize,
        region: packageDetail.region?.name,
        type: packageDetail.type,
        bestSeason: packageDetail.bestSeason,
        grade: packageDetail.grade
    }

    // Use API data for hero section
    const heroImage = packageDetail.mainImage || packageDetail.thumbnail || "/images/fastpacking/hero/image1.webp"
    const heroTitle = packageDetail.name

    // Create highlights from API or use defaults
    const highlights: HighlightItem[] = packageDetail.highlights?.description 
        ? [{ text: packageDetail.highlights.description }]
        : [
            { text: `Experience the magnificent ${packageDetail.name}` },
            { text: `Trek through the stunning ${packageDetail.region?.name} region` },
            { text: `Reach maximum altitude of ${packageDetail.altitude}m` },
            { text: `${packageDetail.grade} difficulty level suitable for experienced trekkers` },
            { text: `Duration: ${packageDetail.duration}` },
            { text: `Best season: ${packageDetail.bestSeason}` }
          ]

    // Overview content from API
    const content = {
        paragraphs: [
            packageDetail.overviewDescription?.replace(/<[^>]*>/g, '') || 
            `The ${packageDetail.name} is an incredible trekking adventure in the ${packageDetail.region?.name} region. This ${packageDetail.grade.toLowerCase()} trek takes you to an altitude of ${packageDetail.altitude}m over ${packageDetail.duration}. Experience the best of Himalayan trekking with ${packageDetail.accomodation} accommodation and enjoy ${packageDetail.meals} throughout your journey.`
        ]
    }

    const note = {
        paragraphs: [
            `This trek has a ${packageDetail.grade.toLowerCase()} difficulty rating. Proper preparation and fitness are essential.`,
            `Best season for this trek is ${packageDetail.bestSeason}. Weather conditions can change rapidly in the mountains.`
        ]
    }

    const briefing = {
        paragraphs: [
            `We'll conduct a pre-trip briefing to discuss the ${packageDetail.name} route, safety guidelines, equipment requirements, and what to expect during your ${packageDetail.duration} adventure.`
        ]
    }

    // Map gallery images from API
    const galleryItems = packageDetail.gallery?.PackageGalleryImage?.length 
        ? packageDetail.gallery.PackageGalleryImage.map((item, index) => ({
            src: item.image,
            alt: item.alt || `${packageDetail.name} - Image ${index + 1}`
          }))
        : [
            { src: heroImage, alt: `${packageDetail.name} - Main Image` },
            { src: "/images/fastpacking/hero/image1.webp", alt: "Trek Image 1" },
            { src: "/images/fastpacking/hero/image2.webp", alt: "Trek Image 2" },
            { src: "/images/fastpacking/hero/image3.webp", alt: "Trek Image 3" },
            { src: "/images/fastpacking/hero/image4.webp", alt: "Trek Image 4" },
            { src: "/images/fastpacking/hero/image5.webp", alt: "Trek Image 5" }
          ]

    // Map itinerary from API
    const detailedItinerary = packageDetail.itinerary?.length 
        ? packageDetail.itinerary.map(item => ({
            day: item.day,
            title: item.title,
            description: item.heading || item.activity,
            activity: item.activity,
            elevation: item.highestAltitude ? `${item.highestAltitude}m` : undefined,
            distance: item.trekDistance || undefined,
            duration: item.trekDuration || undefined,
            drivingTime: item.drivingHours ? `${item.drivingHours} hrs` : undefined,
            flightTime: item.flightHours ? `${item.flightHours} hrs` : undefined,
            imageSrc: item.image || "/images/fastpacking/hero/image1.webp",
            imageAlt: `Day ${item.day} - ${item.title}`
          }))
        : []

    // Create short itinerary from detailed itinerary
    const shortItinerary: DaySummary[] = packageDetail.itinerary?.length
        ? packageDetail.itinerary.map(item => ({
            day: item.day,
            title: item.title
          }))
        : []

    // Map reviews from API
    const apiReviews: Review[] = packageDetail.review?.filter(review => review.rating && review.comment)
        .map((review, index) => ({
            id: index + 1,
            name: review.name,
            role: "Trekker",
            company: "Adventure Enthusiast",
            rating: review.rating,
            content: review.comment,
            avatar: review.reviewImage || `/images/review/${(index % 5) + 1}.png`,
        })) || []

    // Default inclusions/exclusions (you may want to get these from API if available)
    const defaultInclusions: Category[] = [
        {
            title: "Guide & Staff",
            icon: <UserCheck className="w-5 h-5 text-dark" />,
            items: [
                "Experienced trekking guide with insurance and accommodation",
                "Porter service (1 porter for 2 trekkers)",
            ],
        },
        {
            title: "Meals & Accommodation",
            icon: <UtensilsCrossed className="w-5 h-5 text-dark" />,
            items: [
                `${packageDetail.meals}`,
                `${packageDetail.accomodation} accommodation`,
            ],
        },
        {
            title: "Permits & Paperwork",
            icon: <FileText className="w-5 h-5 text-dark" />,
            items: [
                "All necessary trekking permits",
                "Conservation area permits where required",
            ],
        },
        {
            title: "Safety & Medical",
            icon: <ShieldCheck className="w-5 h-5 text-dark" />,
            items: [
                "First Aid kit and emergency evacuation arrangements",
                "Comprehensive travel insurance guidance",
            ],
        },
    ]

    const defaultExclusions: Category[] = [
        {
            title: "Personal Expenses",
            icon: <Utensils className="w-5 h-5 text-dark" />,
            items: [
                "Personal equipment and gear",
                "Travel insurance (mandatory)",
            ],
        },
        {
            title: "Tips & Gratuities",
            icon: <HandCoins className="w-5 h-5 text-dark" />,
            items: [
                "Tips for guides and porters",
                "Personal expenses and souvenirs",
            ],
        },
        {
            title: "Emergency Costs",
            icon: <AlertTriangle className="w-5 h-5 text-dark" />,
            items: [
                "Emergency evacuation costs",
                "Additional costs due to unforeseen circumstances",
            ],
        },
    ]

    // Sample videos (you might get these from API ytVideo field)
    const sampleVideos: ReviewVideo[] = [
        { id: "1", title: `${packageDetail.name} Journey`, youtubeId: "ReJM3C2j-ZU" },
        { id: "2", title: "Mountain Views", youtubeId: "bFy6jTEHlzQ" },
        { id: "3", title: "Trek Highlights", youtubeId: "mfQ31ybmPuA" },
    ]

    return (
        <>
            <div className="relative md:h-screen h-[450px] min-h-[300px] overflow-hidden">
                <HeroSection
                    imageSrc={heroImage}
                    imageAlt={`${heroTitle} - Trek in ${packageDetail.region?.name}`}
                    title={heroTitle}
                />
            </div>

            <div className="container mx-auto px-2 md:px-4 py-8">
                <ScrollSpyTabs sectionIds={sectionIds} />

                <div className="flex flex-col lg:flex-row gap-8">
                    <div className="order-1 lg:order-2 lg:w-80 xl:w-96">
                        <div className="sticky top-40 space-y-6">
                            <PricingCard
                                duration={packageDetail.duration}
                                originalPrice={`USD ${packageDetail.price}`}
                                currentPrice={`USD ${packageDetail.discountPrice || packageDetail.price} pp`}
                                onInquireNow={handleInquireNow}
                                onDatesPrice={handleDatesPrice}
                                onBookNow={handleBookNow}
                            />
                        </div>
                    </div>
                    
                    <div className="order-2 lg:order-1 flex-1 space-y-8">
                        <section id="trek-info" className="scroll-mt-[200px]">
                            <div>
                                <TrekInfo trekData={customTrekData} />
                            </div>

                            <hr className="w-full h-px bg-gray-200 border-0 my-2" />

                            <div>
                                <div className="prose prose-lg max-w-none">
                                    <div className="text-gray-700 text-lg leading-relaxed space-y-4">
                                        <div 
                                            className="text-justify"
                                            dangerouslySetInnerHTML={{ 
                                                __html: packageDetail.overviewDescription || content.paragraphs[0]
                                            }}
                                        />
                                    </div>
                                </div>
                            </div>
                        </section>

                        <section id="highlights" className="scroll-mt-[200px]">
                            <Highlight items={highlights} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="overview" className="scroll-mt-[200px]">
                            <Overview
                                content={content}
                                note={note}
                                briefing={briefing}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />

                            {shortItinerary.length > 0 && (
                                <>
                                    <ItinerarySummary
                                        title={`${packageDetail.name} – ${shortItinerary.length} Days Short Itinerary`}
                                        days={shortItinerary}
                                    />
                                    <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                                </>
                            )}
                        </section>

                        <section id="gallery" className="scroll-mt-[200px]">
                            <GalleryWithMore items={galleryItems} />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="itinerary" className="scroll-mt-[200px]">
                            {detailedItinerary.length > 0 ? (
                                <ItineraryDetailed days={detailedItinerary} />
                            ) : (
                                <div className="bg-white p-6 rounded-lg">
                                    <h2 className="text-2xl font-bold mb-4">Trek Itinerary</h2>
                                    <p className="text-gray-600">Detailed itinerary will be updated soon.</p>
                                </div>
                            )}
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="map" className="scroll-mt-[200px]">
                            <Mapandchart
                                imageSrc={`/images/map/trekking/${packageDetail.slug}-map.webp`}
                                altText={`${packageDetail.name} Route Map showing the complete trek with key landmarks, villages, and elevation points`}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="inclusions" className="scroll-mt-[200px]">
                            <TrekInclusionsExclusions
                                inclusions={defaultInclusions}
                                exclusions={defaultExclusions}
                            />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="gears" className="scroll-mt-[200px]">
                            <Gears />
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="package-info" className="scroll-mt-[200px]">
                            <div className="bg-white p-6 rounded-lg">
                                <h2 className="text-2xl font-bold mb-4">Package Information</h2>
                                <div className="grid md:grid-cols-2 gap-6">
                                    <div>
                                        <h3 className="font-semibold mb-2">Trek Details</h3>
                                        <ul className="space-y-1 text-sm">
                                            <li><strong>Duration:</strong> {packageDetail.duration}</li>
                                            <li><strong>Max Altitude:</strong> {packageDetail.altitude}m</li>
                                            <li><strong>Grade:</strong> {packageDetail.grade}</li>
                                            <li><strong>Group Size:</strong> {packageDetail.groupSize}</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h3 className="font-semibold mb-2">Logistics</h3>
                                        <ul className="space-y-1 text-sm">
                                            <li><strong>Transport:</strong> {packageDetail.transport}</li>
                                            <li><strong>Accommodation:</strong> {packageDetail.accomodation}</li>
                                            <li><strong>Meals:</strong> {packageDetail.meals}</li>
                                            <li><strong>Activities:</strong> {packageDetail.activityPerDay}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                        </section>

                        <section id="reviews" className="scroll-mt-[200px]">
                            <TravelersReview
                                videos={sampleVideos}
                                onWatchMore={() => window.open("https://youtube.com/yourchannel", "_blank")}
                            />

                            <hr className="w-full h-px bg-dark/30 border-0 mb-4" />
                            
                            {apiReviews.length > 0 && (
                                <StackedReviews
                                    reviews={apiReviews}
                                    headerTitle="Customer Reviews"
                                />
                            )}
                        </section>
                    </div>
                </div>
            </div>
            <FAQSection />
        </>
    )
}

export default TrekDetailPage